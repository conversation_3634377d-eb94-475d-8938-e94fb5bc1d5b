############################
# Create the Execution Role
############################
data "aws_iam_policy_document" "ecs_task_execution_role" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "promxy_execution_role" {
  name               = "promxy-execution-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_execution_role.json
  path               = "/"

  tags = merge(var.default_tags, { Name = "promxy-execution-role" })
}

# Attach the standard AmazonECSTaskExecutionRolePolicy
# which allows pulling images, writing logs, etc.
resource "aws_iam_role_policy_attachment" "promxy_execution_role_attachment" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
  role       = aws_iam_role.promxy_execution_role.name
}

############################
# Create the Task Role
############################
data "aws_iam_policy_document" "ecs_task_role" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "promxy_task_role" {
  name               = "promxy-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_task_role.json
  path               = "/"

  tags = merge(var.default_tags, { Name = "promxy-task-role" })
}

############################
# ECS Task Definition
############################
resource "aws_ecs_task_definition" "promxy" {
  family                   = "promxy-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = var.task_cpu
  memory                   = var.task_memory

  # Use the roles created above
  execution_role_arn = aws_iam_role.promxy_execution_role.arn
  task_role_arn      = aws_iam_role.promxy_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "promxy"
      image     = var.promxy_image
      essential = true
      entryPoint = [
        "/bin/sh",
        "-c",
      ]
      command = [
        "mkdir -p /tmp/config && echo \"$PROMXY_CONFIG\" | base64 -d > /tmp/config/config.yml && /bin/promxy --config=/tmp/config/config.yml"
      ]
      environment = [
        {
          name  = "PROMXY_CONFIG"
          value = base64encode(data.template_file.promxy_config.rendered)
        }
      ]
      portMappings = [
        {
          containerPort = 8082
          hostPort      = 8082
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-region"        = var.aws_region
          "awslogs-group"         = "/ecs/promxy"
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])
}

resource "aws_cloudwatch_log_group" "promxy" {
  name              = "/ecs/promxy"
  retention_in_days = 7 # Keep logs for 7 days
  tags = {
    Application = "Promxy"
  }
}

resource "aws_service_discovery_service" "promxy" {
  name = "promxy"
  dns_config {
    namespace_id = var.namespace_id

    dns_records {
      type = "A"
      ttl  = 10
    }

    routing_policy = "MULTIVALUE"
  }
  health_check_custom_config {
    failure_threshold = 1
  }

  tags = merge(var.default_tags, { Name = "promxy" })
}


resource "aws_ecs_service" "promxy" {
  name            = var.service_name
  cluster         = var.ecs_cluster_id
  task_definition = aws_ecs_task_definition.promxy.arn
  desired_count   = var.desired_count
  launch_type     = "FARGATE"

  network_configuration {
    subnets          = var.subnets
    assign_public_ip = true
    security_groups  = [aws_security_group.promxy_sg.id]
  }

  service_registries {
    registry_arn = aws_service_discovery_service.promxy.arn
  }
}

resource "aws_security_group" "promxy_sg" {
  name        = "promxy-sg"
  description = "Security group for Promxy"
  vpc_id      = var.vpc_id

  ingress {
    description     = "Allow inbound from Grafana on 8082"
    from_port       = 8082
    to_port         = 8082
    protocol        = "tcp"
    security_groups = var.grafana_security_groups
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.default_tags, { Name = "promxy-sg" })
}
