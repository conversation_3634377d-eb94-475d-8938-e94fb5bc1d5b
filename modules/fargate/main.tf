resource "aws_ssm_parameter" "fargate_app" {
  count     = var.env_vars != tolist([{ "name" = "EMPTY_LIST" }]) ? length(var.env_vars) : 0
  name      = "${var.secret_managager_prefix}${var.env_vars[count.index].name}"
  type      = var.env_vars[count.index].type
  value     = var.env_vars[count.index].value
  tags      = merge(var.default_tags, var.tags)
  overwrite = true
}

resource "aws_iam_role" "ecs_exec_task" {
  name = "${var.fargate_application_name}FargateExecTaskRole"
  assume_role_policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Sid : "${var.fargate_application_name}FargateExecTaskRole",
        Effect : "Allow",
        Action : "sts:AssumeRole",
        Principal : {
          Service : [
            "ecs-tasks.amazonaws.com"
          ]
        }
      }
    ]
  })
  tags = merge(var.default_tags, var.tags)
}

resource "aws_iam_role_policy_attachment" "ecs_exec_task_custom_policy" {
  role       = aws_iam_role.ecs_exec_task.name
  policy_arn = aws_iam_policy.ecs_exec_task.arn
}

resource "aws_iam_role_policy_attachment" "ecs_exec_task_execution_policy" {
  role       = aws_iam_role.ecs_exec_task.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_policy" "ecs_exec_task" {
  name = "${var.fargate_application_name}FargateExecTaskRole"
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "ssm:Get*"
        ],
        "Resource" : [
          "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter${var.secret_managager_prefix}*",
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "logs:CreateLogGroup"
        ],
        "Resource" : [
          "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/fargate/service/${var.fargate_application_name}:*",
        ]
      }
    ]
    }
  )
  tags = merge(var.default_tags, var.tags)
}

resource "aws_ecs_task_definition" "fargate_app" {
  family                   = var.fargate_application_name
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = var.ecs_task_cpu
  memory                   = var.ecs_task_memory
  execution_role_arn       = aws_iam_role.ecs_exec_task.arn

  container_definitions = jsonencode([
    {
      name      = var.fargate_application_name
      image     = var.container_image
      cpu       = var.ecs_task_cpu
      memory    = var.ecs_task_memory
      essential = true
      portMappings = [
        {
          containerPort = var.container_port
          hostPort      = var.container_port
        }
      ],
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-create-group  = "true"
          awslogs-group         = "/aws/fargate/service/${var.fargate_application_name}"
          awslogs-region        = data.aws_region.current.name
          awslogs-stream-prefix = var.fargate_application_name
        }
      }
      secrets = var.env_vars != tolist([{ "name" = "EMPTY_LIST" }]) ? toset([for env_var in var.env_vars : { "name" = env_var.name, "valueFrom" = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter${var.secret_managager_prefix}${env_var.name}" }]) : null
    }
  ])
  tags = merge(var.default_tags, var.tags)
  depends_on = [
    aws_cloudwatch_log_group.fargate_log_group
  ]
}

resource "aws_cloudwatch_log_group" "fargate_log_group" {
  name              = "/aws/fargate/service/${var.fargate_application_name}"
  retention_in_days = 30
}

resource "aws_ecs_cluster" "fargate_app" {
  name = "${var.fargate_application_name}Cluster"
  tags = merge(var.default_tags, var.tags)
}

resource "aws_ecs_cluster_capacity_providers" "fargate_app" {
  cluster_name       = aws_ecs_cluster.fargate_app.name
  capacity_providers = ["FARGATE", "FARGATE_SPOT"]
  default_capacity_provider_strategy {
    capacity_provider = "FARGATE"
  }
}

resource "aws_security_group" "fargate_app" {
  name        = "${var.fargate_application_name}ECSSecGroup"
  description = "Allows traffic coming from ALB to ECS service"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = var.container_port
    to_port         = var.container_port
    protocol        = "TCP"
    security_groups = [(var.create_alb ? aws_security_group.fargate_load_balancer[0].id : var.external_alb_security_group_id)]
  }
  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
  tags = merge(var.default_tags, var.tags)
}

resource "aws_ecs_service" "fargate_app" {
  name            = "${var.fargate_application_name}Service"
  cluster         = aws_ecs_cluster.fargate_app.id
  task_definition = aws_ecs_task_definition.fargate_app.arn
  desired_count   = var.fargate_app_ecs_service_desired_count

  network_configuration {
    assign_public_ip = true
    security_groups  = concat([aws_security_group.fargate_app.id], var.fargate_app_security_group_ids)
    subnets          = var.fargate_subnets_ids
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.fargate_load_balancer.arn
    container_name   = var.fargate_application_name
    container_port   = var.container_port
  }

  capacity_provider_strategy {
    capacity_provider = "FARGATE"
    weight            = 100
  }
  tags = merge(var.default_tags, var.tags)
}

resource "aws_security_group" "fargate_load_balancer" {
  count       = var.create_alb ? 1 : 0
  name        = "${var.fargate_application_name}ALBSecGRoup"
  description = "Allow inbound traffic to Fargate ECS"
  vpc_id      = var.vpc_id

  ingress {
    description      = "Allow inbound traffic to Fargate ECS"
    from_port        = 80
    to_port          = 80
    protocol         = "TCP"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "Allow inbound traffic to Fargate ECS"
    from_port        = 443
    to_port          = 443
    protocol         = "TCP"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
  tags = merge(var.default_tags, var.tags, { Name = "${var.fargate_application_name}ALBSecGRoup" })
}

resource "aws_alb_target_group" "fargate_load_balancer" {
  # https://docs.aws.amazon.com/AmazonECS/latest/userguide/create-application-load-balancer.html
  name        = "${var.fargate_application_name}ALB"
  target_type = "ip"
  port        = var.container_port
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  health_check {
    path                = var.fargate_app_target_group_health_check_path
    healthy_threshold   = var.fargate_app_target_group_health_check_healthy_threshold
    unhealthy_threshold = var.fargate_app_target_group_health_check_unhealthy_threshold
  }
  tags = merge(var.default_tags, var.tags)
}


resource "aws_alb_listener" "fargate_app" {
  count             = var.create_alb ? 1 : 0
  load_balancer_arn = var.create_alb ? aws_alb.fargate_app[0].id : var.external_alb_id
  port              = tostring(var.load_balancer_listen_port)
  protocol          = var.load_balancer_listen_protocol

  default_action {
    order = 1
    type  = "redirect"
    redirect {
      host        = "#{host}"
      path        = "/#{path}"
      port        = "443"
      protocol    = "HTTPS"
      query       = "#{query}"
      status_code = "HTTP_301"
    }
  }
  tags = merge(var.default_tags, var.tags)
}


resource "aws_alb" "fargate_app" {
  count              = var.create_alb ? 1 : 0
  name               = "${var.fargate_application_name}ALB"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.fargate_load_balancer[0].id]
  subnets            = var.alb_subnets_ids

  enable_deletion_protection = var.load_balancer_deletetion_protection
  tags                       = merge(var.default_tags, var.tags)
}

resource "aws_appautoscaling_target" "fargate_scaling_target" {
  max_capacity       = var.max_capacity
  min_capacity       = var.min_capacity
  resource_id        = "service/${aws_ecs_cluster.fargate_app.name}/${aws_ecs_service.fargate_app.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "fargate_cpu_scaling_policy" {
  name               = "fargate-ecs-cpu-scaling-policy"
  service_namespace  = "ecs"
  resource_id        = aws_appautoscaling_target.fargate_scaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.fargate_scaling_target.scalable_dimension
  policy_type        = "TargetTrackingScaling"

  target_tracking_scaling_policy_configuration {
    target_value = 50.0
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}

resource "aws_appautoscaling_policy" "fargate_memory_scaling_policy" {
  name               = "fargate-ecs-memory-scaling-policy"
  service_namespace  = "ecs"
  resource_id        = aws_appautoscaling_target.fargate_scaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.fargate_scaling_target.scalable_dimension
  policy_type        = "TargetTrackingScaling"

  target_tracking_scaling_policy_configuration {
    target_value = 60.0
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}
