
resource "aws_vpc" "vpc" {
  cidr_block           = var.vpc_cidr_block
  enable_dns_support   = "true"
  enable_dns_hostnames = "true"

  tags = merge(var.tags, { Name = var.vpc_name })
}

resource "aws_subnet" "public" {
  count = length(var.public_subnets)

  availability_zone       = length(regexall("^[a-z]{2}-", element(var.azs, count.index))) > 0 ? element(var.azs, count.index) : null
  cidr_block              = element(concat(var.public_subnets, [""]), count.index)
  map_public_ip_on_launch = true
  vpc_id                  = aws_vpc.vpc.id
  tags = merge(
    var.tags,
    var.public_subnet_tags,
    { SubnetType = "public", Name = format("${var.vpc_name} - public - %s", element(var.azs, count.index)) }
  )
}

resource "aws_subnet" "private" {
  count = length(var.private_subnets)

  availability_zone       = length(regexall("^[a-z]{2}-", element(var.azs, count.index))) > 0 ? element(var.azs, count.index) : null
  cidr_block              = element(concat(var.private_subnets, [""]), count.index)
  map_public_ip_on_launch = false
  vpc_id                  = aws_vpc.vpc.id
  tags = merge(
    var.tags,
    var.private_subnet_tags,
    { SubnetType = "private", Name = format("${var.vpc_name} - private - %s", element(var.azs, count.index)) }
  )
}

resource "aws_internet_gateway" "gateway" {
  vpc_id = aws_vpc.vpc.id
  tags   = merge(var.tags, { Name = var.vpc_name })
}

resource "aws_eip" "nat_eip" {
  domain = "vpc"
  tags   = merge(var.tags, { Name = "${var.vpc_name} - NAT EIP" })
}

resource "aws_nat_gateway" "nat" {
  allocation_id = aws_eip.nat_eip.id
  subnet_id     = aws_subnet.public[0].id
  tags          = merge(var.tags, { Name = var.vpc_name })
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.vpc.id
  tags   = merge(var.tags, { Name = "${var.vpc_name} - public route" })
}

resource "aws_route" "public" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.gateway.id
}

resource "aws_route_table_association" "public" {
  count          = length(var.public_subnets)
  route_table_id = aws_route_table.public.id
  subnet_id      = aws_subnet.public[count.index].id
}

resource "aws_route_table" "private" {
  vpc_id = aws_vpc.vpc.id
  tags   = merge(var.tags, { Name = "${var.vpc_name} - private route" })
}

resource "aws_route" "private" {
  route_table_id         = aws_route_table.private.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat.id
}

resource "aws_route" "additional_routes" {
  for_each = var.allow_additional_routes ? var.additional_routes : {}

  route_table_id         = aws_route_table.private.id
  destination_cidr_block = each.key
  network_interface_id   = each.value
}

resource "aws_route_table_association" "private" {
  count          = length(var.private_subnets)
  route_table_id = aws_route_table.private.id
  subnet_id      = aws_subnet.private[count.index].id
}

resource "aws_security_group" "havelock_north_access" {
  count       = var.havelocknorthaccess_sg == "enabled" ? 1 : 0
  name        = "HavelockNorthAccess"
  description = "Allow full access from HN office"
  vpc_id      = aws_vpc.vpc.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**************/32"]
    description = "Fingermark Office"
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**************/32"]
    description = "Wireguard IP"
  }

  tags = var.tags
}
