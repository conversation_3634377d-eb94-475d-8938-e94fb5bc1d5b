resource "aws_cloudwatch_log_group" "function_log_group" {
  count             = var.enable_logging == "false" ? 0 : 1
  name              = "/aws/lambda/${aws_lambda_function.lambda.function_name}"
  retention_in_days = 7
  lifecycle {
    prevent_destroy = false
  }
  tags = var.tags
}

resource "aws_iam_role" "iam_for_lambda" {
  name               = var.app_name
  tags               = var.tags
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_policy" "function_logging_policy" {
  count = var.enable_logging == "false" ? 0 : 1
  name  = var.app_name
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        Action : [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Effect : "Allow",
        Resource : "arn:aws:logs:${var.aws_region}:${var.aws_account_id}:log-group:/aws/lambda/${aws_lambda_function.lambda.function_name}"
      }
    ]
  })
  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "iam_for_lambda_policy_attach" {
  role       = aws_iam_role.iam_for_lambda.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "function_logging_policy_attachment" {
  count      = var.enable_logging == "false" ? 0 : 1
  role       = aws_iam_role.iam_for_lambda.name
  policy_arn = aws_iam_policy.function_logging_policy[0].arn
}


resource "aws_lambda_function" "lambda" {
  function_name = var.app_name
  role          = aws_iam_role.iam_for_lambda.arn
  image_uri     = var.ecr_image
  package_type  = "Image"
  timeout       = var.lambda_timeout
  tags          = var.tags
  lifecycle {
    ignore_changes = [
      image_uri
    ]
  }
  environment {
    variables = var.environment_variables_for_lambda
  }
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  arn            = aws_lambda_function.lambda.arn
  rule           = var.event_rule_name
  event_bus_name = var.event_bus_name
}

resource "aws_lambda_permission" "allow_eventbridge_permissions_for_lambda" {
  statement_id  = "AllowExecutionFromEventBridgeRule"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = var.event_rule_arn
}
