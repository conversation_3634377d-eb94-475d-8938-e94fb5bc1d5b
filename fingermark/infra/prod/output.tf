
output "grafana_db_master_password" {
  description = "The master password"
  value       = module.grafana_server.grafana_db_master_password
  sensitive   = true
}

output "aurora_cluster_endpoint" {
  description = "The DB endpoint"
  value       = module.grafana_server.grafana_aurora_cluster_endpoint
  sensitive   = true
}

output "eyecue_grafana_user" {
  value     = module.grafana_server.grafana_user
  sensitive = true
}

output "icinga2_master_db_passwd" {
  value     = module.icinga2_master_db.db_instance_password
  sensitive = true
}

output "softether_monitoring_api_ec2_public_ip" {
  value = module.softether_monitoring_api.ec2_public_ip
}

output "config_map_aws_auth" {
  description = "A kubernetes configuration to authenticate to this EKS cluster."
  value       = module.eks.config_map_aws_auth
}

output "kubeconfig" {
  description = "kubectl config file contents for this EKS cluster."
  value       = module.eks.kubeconfig
}

output "eks_worker_node_role_arn" {
  description = "The ARN of the EKS worker node role."
  value       = module.eks.worker_node_role_arn
}

output "infra_workstation_service_directory_pass" {
  description = "AWS Directory Service Password"
  value       = random_string.infra_workstation.result
}
