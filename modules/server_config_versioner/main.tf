module "server_config_s3" {
  source = "terraform-aws-modules/s3-bucket/aws"
  bucket = "fingermark-${var.server_config_name}-configuration-backup"
  acl    = var.server_config_s3_acl

  versioning = {
    enabled = var.server_config_s3_versioning
  }

  tags = merge(var.default_tags, var.tags)
}


resource "aws_iam_policy" "server_config_s3_access" {
  name        = "${var.server_config_name}AccessToS3ServerConfig"
  path        = "/${var.stage}/serverconfig/${var.server_config_name}/"
  description = "Give ${var.server_config_name} instance access to the server config bucket"
  tags        = var.default_tags
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:List*",
          "s3:Put*"
        ]
        Effect   = "Allow"
        Resource = [module.server_config_s3.s3_bucket_arn, "${module.server_config_s3.s3_bucket_arn}/*"]
      },
      {
        Action   = ["s3:GetObject"]
        Effect   = "Allow"
        Resource = [module.server_config_s3.s3_bucket_arn]
      }
    ]
  })
}


resource "aws_iam_role" "server_config_s3_access" {
  name = "${var.server_config_name}AccessToS3ServerConfig"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Sid    = "${var.server_config_name}AccessToS3ServerConfig"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
  tags = merge(var.default_tags, var.tags)
}

resource "aws_iam_role_policy_attachment" "server_config_s3_access_policy" {
  role       = aws_iam_role.server_config_s3_access.name
  policy_arn = aws_iam_policy.server_config_s3_access.arn
}

resource "aws_iam_role_policy_attachment" "additional_policies" {
  for_each   = toset(var.additional_policy_arns)
  role       = aws_iam_role.server_config_s3_access.name
  policy_arn = each.value
}

resource "aws_iam_instance_profile" "server_config" {
  name = "${var.server_config_name}AccessToS3ServerConfig"
  role = aws_iam_role.server_config_s3_access.name
  tags = merge(var.default_tags, var.tags)
}
