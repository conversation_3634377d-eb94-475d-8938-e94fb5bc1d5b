module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}

module "vanta" {
  source = "../../../../modules/vanta"
}

module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "northvue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.product}_${var.env}_${data.aws_region.current.name}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "northvue_cloudfront_cert" {
  source = "../../../../modules/northvue_cloudfront_cert"
}

resource "aws_iam_user" "northvue_deployer" {
  name = "northvue_deployer"
  tags = var.default_tags
}

resource "aws_iam_user_policy_attachment" "northvue_deployer" {
  user       = aws_iam_user.northvue_deployer.name
  policy_arn = aws_iam_policy.northvue_deployer.arn
}

resource "aws_iam_policy" "northvue_deployer" {
  name = "northvue_deployer-iam-user-policy"

  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
		{
			"Effect": "Allow",
			"Action": [
				"ecr:BatchGet*",
				"ecr:BatchCheck*",
				"ecr:Get*",
				"ecr:Describe*",
				"ecr:List*"
			],
			"Resource": [
			    "*"
			]
		},
    {
      "Effect" : "Allow",
      "Action" : [
        "s3:List*",
        "s3:Get*"
      ],
      "Resource" :[
        "*"
      ]
    }
  ]
}
POLICY
}

module "northvue_monitor_site_configs" {
  source      = "../../../../modules/s3_bucket"
  bucket_name = "northvue-monitor-site-configs"
  region      = data.aws_region.current.name
  tags        = var.default_tags
  acl         = null
}

module "northvue_monitor_model_weights" {
  source      = "../../../../modules/s3_bucket"
  bucket_name = "northvue-monitor-model-weights"
  region      = data.aws_region.current.name
  tags        = var.default_tags
  acl         = null
}

resource "aws_acm_certificate" "cert_cdn_monitor" {
  domain_name       = "cdn.monitor.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = var.default_tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "cert_api_monitor" {
  domain_name       = "api.monitor.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = var.default_tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_amplify_app" "monitor_dashboard" {
  name         = "monitor-dashboard"
  repository   = "https://bitbucket.org/fingermarkltd/monitor-dashboard"
  access_token = data.vault_generic_secret.amplify_repository_key.data["secret"]
  build_spec   = <<-EOT
    version: 1
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
  EOT

  custom_rule {
    source = "https://monitor.eyecuedashboard.com"
    status = "302"
    target = "https://www.monitor.eyecuedashboard.com"
  }
  custom_rule {
    source = "/<*>"
    status = "404-200"
    target = "/index.html"
  }
  custom_rule {
    source = "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|ttf|map|json)$)([^.]+$)/>"
    status = "200"
    target = "/index.html"
  }

  environment_variables = {
    REACT_APP_AUTH_AUDIENCE     = "http://api.monitor.eyecuedashboard.com",
    REACT_APP_AUTH_CLIENT_ID    = "1hE8I1Nbulo2EGLaCxLVSUXIaGFiyxi0",
    REACT_APP_AUTH_DOMAIN       = "https://fingermark.au.auth0.com",
    REACT_APP_CATCH_API_URL     = "https://api.monitor.eyecuedashboard.com",
    REACT_APP_PUBLIC_VIDEO_HOST = "https://cdn.monitor.eyecuedashboard.com"
  }
}

resource "aws_amplify_domain_association" "amplify_monitor_dashboard_domain" {
  app_id                = aws_amplify_app.monitor_dashboard.id
  domain_name           = "monitor.eyecuedashboard.com"
  wait_for_verification = true

  sub_domain {
    branch_name = "release"
    prefix      = ""
  }

  sub_domain {
    branch_name = "release"
    prefix      = "www"
  }
}

module "eyecue_iot" {
  source         = "../../../../modules/eyecue_iot"
  aws_iam_user   = "eyecue-iot-creator"
  client_name    = var.product
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = data.aws_region.current.name
}


module "kinesis_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.retention_period
  stream_mode                    = var.stream_mode
  stream_name_list               = var.stream_name_list
  firehose_roi_bucket_arn        = "arn:aws:s3:::fm-prod-datalake-1-ap-southeast-2"
  current_account_id             = data.aws_caller_identity.current.account_id
}

module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = var.CLIENT_NAME
  kinesis_iot_topic_rules_config = var.kinesis_iot_topic_rules_config
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  dynamodb_cw_alarms = {
    defaults = {
      us_east_1 = {
        tables_config = merge(
          var.dynamodb_cw_alarms_defaults_tables_us_east_1,
        )
        gsis_config = merge(
          var.dynamodb_cw_alarms_defaults_gsis_us_east_1
        )
      }
    }
  }
}

module "dynamodb_cw_alarms_defaults_us_east_1" {
  source                                 = "../../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.us_east_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.us_east_1.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.us_east_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.us_east_1.gsis_config
  tags         = var.tags
  default_tags = var.default_tags
}
