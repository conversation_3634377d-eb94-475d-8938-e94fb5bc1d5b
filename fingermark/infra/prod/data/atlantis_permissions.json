{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": "sts:<PERSON><PERSON>Role", "Resource": "arn:aws:iam::*:role/AdminAccess", "Condition": {"ForAnyValue:StringLike": {"aws:PrincipalOrgPaths": "o-aydhjv9alg/*/"}}}, {"Effect": "Allow", "Action": "sts:<PERSON><PERSON>Role", "Resource": ["arn:aws:iam::055313672806:role/TerraformBackendAccess", "arn:aws:iam::577602312581:role/TerraformBackendAccess"]}]}