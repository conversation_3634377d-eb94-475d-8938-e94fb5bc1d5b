variable "aws_iam_user" {}

variable "aws_iam_user_list" {
  type    = list(string)
  default = ["grafana"]
}

variable "keybase" {
  default = "keybase:fingermark"
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "create_attachment_policy" {
  type    = bool
  default = true
}
