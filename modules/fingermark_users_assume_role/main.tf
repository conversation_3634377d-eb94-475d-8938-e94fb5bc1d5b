locals {
  role_policies = {
    "AdminAccess"    = data.aws_iam_policy_document.instance_assume_role_policy_admin_access.json
    "DeployerAccess" = data.aws_iam_policy_document.instance_assume_role_deployer_policy.json
    "SupportAccess"  = data.aws_iam_policy_document.instance_assume_role_policy.json
    "ICXeedAccess"   = data.aws_iam_policy_document.instance_assume_role_policy.json
    "default"        = data.aws_iam_policy_document.instance_assume_role_policy.json
  }

  # Define base policies and enhanced policies separately
  readonly_base_policies = [
    "arn:aws:iam::aws:policy/ReadOnlyAccess",
    aws_iam_policy.reading_access.arn
  ]

  readonly_enhanced_policies = var.enhanced_readonly_security_access ? [
    "arn:aws:iam::aws:policy/SecurityAudit",
    aws_iam_policy.control_tower_readonly_policy.arn
  ] : []

  # Combine policies based on the flag
  readonly_policies = concat(local.readonly_base_policies, local.readonly_enhanced_policies)

  # Map of AWS managed policies to be attached to specific roles
  role_managed_policies = {
    "DevAccess" = [
      "arn:aws:iam::aws:policy/AWSBillingReadOnlyAccess",
      "arn:aws:iam::aws:policy/AWSStepFunctionsReadOnlyAccess"
    ],
    "PowerAccess" = [
      "arn:aws:iam::aws:policy/AWSBillingReadOnlyAccess",
      "arn:aws:iam::aws:policy/AWSStepFunctionsReadOnlyAccess"
    ],
    "DeployerAccess" = [
      "arn:aws:iam::aws:policy/AWSStepFunctionsFullAccess"
    ]
  }
}

resource "aws_iam_role" "fingermark_external_access" {
  for_each             = toset(var.roles)
  name                 = each.value
  path                 = "/"
  max_session_duration = var.fm_assume_role_session_duration
  assume_role_policy   = coalesce(lookup(local.role_policies, each.value, null), local.role_policies["default"])
  tags                 = var.tags
}

resource "aws_iam_role_policy_attachment" "assume_role_policy" {
  for_each   = toset(var.roles)
  role       = aws_iam_role.fingermark_external_access[each.value].name
  policy_arn = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:policy/${each.value}Policy"
}

# Attach AWS managed policies to roles based on the role_managed_policies map
resource "aws_iam_role_policy_attachment" "managed_policy_attachments" {
  for_each = {
    for pair in flatten([
      for role, policies in local.role_managed_policies : [
        for policy in policies : {
          role   = role
          policy = policy
        } if contains(var.roles, role)
      ]
    ]) : "${pair.role}-${replace(basename(pair.policy), "/", "-")}" => pair
  }

  role       = aws_iam_role.fingermark_external_access[each.value.role].name
  policy_arn = each.value.policy
}

## ReadOnly Role (always created)
resource "aws_iam_role" "read_only_role" {
  name                 = "ReadOnlyAccess"
  path                 = "/"
  max_session_duration = 3600
  assume_role_policy   = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${var.trusted_aws_account_id}:root"
      },
      "Action": "sts:AssumeRole",
      "Condition": {}
    }
  ]
}
POLICY
}

resource "aws_iam_role_policy_attachment" "read_only_role_policies" {
  count      = length(local.readonly_policies)
  role       = aws_iam_role.read_only_role.name
  policy_arn = local.readonly_policies[count.index]
}

#role for security team to access artifacts

resource "aws_iam_role" "security_team_role" {
  name = "SecurityTeamAccess"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::${var.trusted_aws_account_id}:root"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "attach_artifact_policy" {
  role       = aws_iam_role.security_team_role.name
  policy_arn = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:policy/SecurityAccessPolicy"
}

resource "aws_iam_openid_connect_provider" "bitbucket-oidc" {
  url = "https://api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc"

  client_id_list = [
    "ari:cloud:bitbucket::workspace/406a3c3c-9e6a-4312-b8c5-5d7af4e86bb3",
  ]

  thumbprint_list = ["a031c46782e6e6c662c2c87c76da9aa62ccabd8e"]
}
