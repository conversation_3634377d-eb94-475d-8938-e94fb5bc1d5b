provider "aws" {
  region = var.AWS_REGION
  assume_role {
    # The role AR<PERSON> within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "ptl-usa"
  }
}

provider "aws" {
  region = "ap-southeast-2"
  alias  = "ap-southeast-2"
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "ptl-usa"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}

