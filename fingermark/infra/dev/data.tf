data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_ami" "default" {
  most_recent = "true"

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]
  }
  # aws ec2 describe-images --image-ids ami-0194c3e07668a7e36 --region eu-west-2 | jq ".Images[0].OwnerId"  
  owners = ["099720109477"]
}

data "vault_generic_secret" "vault_secrets" {
  path = "secret/terraform/francium/secrets"
}
