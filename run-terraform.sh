#!/bin/bash

# Example usage:
# ./run-terraform.sh init -accounts=mcd,cfa
# ./run-terraform.sh plan -target=module.eyecue_network (for all accounts under eyecue)
# ./run-terraform.sh plan -target=module.eyecue_network -accounts=mcd,cfa
# ./run-terraform.sh apply -target=module.eyecue_network -accounts=mcd,cfa
# ./run-terraform.sh apply -target=module.eyecue_network -accounts=mcd -auto-approve


set -e # Exit on error

EYECUE_DIR="./fingermark/eyecue"
OPERATION=""
ACCOUNTS=()
TARGET_OPTIONS=()
AUTO_APPROVE=false
DRY_RUN=false
EXTRA_ARGS=""

# Parse command line arguments
parse_args() {
    OPERATION=$1
    shift

    while [[ $# -gt 0 ]]; do
        case $1 in
            -target=*)
                TARGET_OPTIONS+=("${1}")
                ;;
            -accounts=*)
                IFS=',' read -ra ACCOUNTS <<< "${1#*=}"
                ;;
            -auto-approve)
                AUTO_APPROVE=true
                ;;
            -dry-run)
                DRY_RUN=true
                ;;
            -help|--help)
                show_help
                exit 0
                ;;
            *)
                EXTRA_ARGS="$EXTRA_ARGS $1"
                ;;
        esac
        shift
    done
}

# Validate operation
validate_operation() {
    if [[ -z "$OPERATION" ]]; then
        echo "Error: Operation (init, plan, apply) is required."
        show_help
        exit 1
    fi

    if [[ "$OPERATION" != "init" && "$OPERATION" != "plan" && "$OPERATION" != "apply" ]]; then
        echo "Error: Invalid operation '$OPERATION'. Please use 'init', 'plan', or 'apply'."
        show_help
        exit 1
    fi
}

# Show help information
show_help() {
    echo "Usage: ./run-terraform.sh [operation] [options]"
    echo
    echo "Operations:"
    echo "  init        Initialize terraform"
    echo "  plan        Create a terraform plan"
    echo "  apply       Apply a terraform plan"
    echo
    echo "Options:"
    echo "  -target=module.name      Target a specific module (can be used multiple times)"
    echo "  -accounts=acc1,acc2      Run only for specified accounts (comma-separated)"
    echo "  -auto-approve            Automatically approve apply (use with caution)"
    echo "  -dry-run                 Print commands without executing them"
    echo "  -help, --help            Show this help message"
    echo "  Any other options will be passed directly to terraform"
    echo
    echo "Examples:"
    echo "  ./run-terraform.sh plan -target=module.eyecue_network"
    echo "  ./run-terraform.sh apply -target=module.eyecue_network -accounts=mcd,cfa"
    echo "  ./run-terraform.sh init -accounts=mcd,cfa"
    echo "  ./run-terraform.sh plan -dry-run -accounts=mcd"
    echo "  ./run-terraform.sh plan -var-file=custom.tfvars"
}

# Confirm operation for apply
confirm_apply() {
    local account=$1
    echo
    read -p "Do you want to apply changes to account $account? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Skipping apply for $account"
        return 1
    fi
    return 0
}

# Function to run terraform command
run_terraform() {
    local dir=$1
    local account=$(basename "$dir")

    # Skip if accounts specified and this one is not in the list
    if [ ${#ACCOUNTS[@]} -gt 0 ]; then
        local found=false
        for acc in "${ACCOUNTS[@]}"; do
            if [[ "$account" == "$acc" ]]; then
                found=true
                break
            fi
        done

        if [ "$found" = false ]; then
            echo "Skipping account $account (not in requested list)"
            return 0
        fi
    fi

    echo "========================================="
    echo "Running terraform $OPERATION for account: $account"
    echo "========================================="

    cd "$dir" || { echo "Failed to change directory to $dir"; return 1; }

    local cmd="terraform $OPERATION"

    # Add target options if specified
    for target in "${TARGET_OPTIONS[@]}"; do
        cmd="$cmd $target"
    done

    # Add extra arguments
    if [ -n "$EXTRA_ARGS" ]; then
        cmd="$cmd $EXTRA_ARGS"
    fi

    # Add auto-approve if specified for apply
    if [ "$OPERATION" == "apply" ] && [ "$AUTO_APPROVE" = true ]; then
        cmd="$cmd -auto-approve"
    fi

    # Execute or just print the command
    echo "Command: $cmd"
    if [ "$DRY_RUN" = true ]; then
        echo "Dry run mode - not executing"
        return 0
    fi

    # For apply, confirm unless auto-approve is set
    if [ "$OPERATION" == "apply" ] && [ "$AUTO_APPROVE" = false ]; then
        confirm_apply "$account" || return 0
    fi

    # Execute the command
    echo "Executing terraform command..."
    eval $cmd
    local result=$?

    if [ $result -ne 0 ]; then
        echo "Command failed with exit code $result"
        return $result
    fi

    echo "Command completed successfully"
    return 0
}

# Main function
main() {
    # Parse arguments
    parse_args "$@"

    # Validate operation
    validate_operation

    # Check if accounts were specified
    if [ ${#ACCOUNTS[@]} -gt 0 ]; then
        echo "Running for accounts: ${ACCOUNTS[*]}"
    else
        echo "Running for all accounts in $EYECUE_DIR"
    fi

    local exit_code=0

    # Iterate through directories
    for dir in "$EYECUE_DIR"/*; do
        if [ -d "$dir" ]; then
            (run_terraform "$dir") || {
                echo "Error in account $(basename "$dir")"
                exit_code=1
            }
        fi
    done

    if [ $exit_code -eq 0 ]; then
        echo "All operations completed successfully."
    else
        echo "Some operations failed. Please check the output above."
    fi

    exit $exit_code
}

# Show help if no arguments provided
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# Execute main function with all arguments
main "$@"
