
variable "aws_region" {
  description = "AWS Region"
  default     = "ap-southeast-2"
  type        = string
}


variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "TRUE"
    Stack       = "supersonic"
    Environment = "prod"
    Customer    = "americana"
  }
}

variable "customer" {
  default     = "americana"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "supersonic"
  description = "Fingermark Product"
  type        = string
}