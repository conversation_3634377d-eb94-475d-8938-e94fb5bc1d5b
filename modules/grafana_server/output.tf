output "grafana_user" {
  value     = module.grafana_user.user
  sensitive = true
}

output "grafana_db_master_password" {
  description = "The Grafana's DB master password"
  value       = module.grafana_db.cluster_master_password
  sensitive   = true
}

output "grafana_instance_profile_name" {
  description = "The IAM role name attached to the EC2"
  value       = aws_iam_instance_profile.CloudWatchAgentServerRole.name
}

output "grafana_alb_dns_name" {
  description = "ALB Name for grafana"
  value       = module.grafana_web.fargate_app_load_balancer_dns_address
}

output "grafana_alb_arn" {
  description = "ALB arn for grafana if provisioned"
  value       = module.grafana_web.fargate_app_lb_arn
}

output "grafana_alb_target_group_arn" {
  description = "ALB arn for grafana target group"
  value       = module.grafana_web.fargate_app_lb_target_group_arn
}

output "grafana_web_security_group_id" {
  description = "Security group of Grafana web"
  value       = module.grafana_web.fargate_app_security_group
}

output "grafana_aurora_cluster_endpoint" {
  description = "The DB endpoint"
  value       = module.grafana_db.cluster_endpoint
  sensitive   = true
}

output "grafana_ecs_cluster_arn" {
  description = "The ECS cluster ARN"
  value       = module.grafana_web.fargate_ecs_cluster_arn
}
