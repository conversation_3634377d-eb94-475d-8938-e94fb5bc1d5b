terraform {
  required_version = "1.7.2"
  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "{{ cookiecutter.aws_region }}"
    key            = "{{ cookiecutter.client_acronym }}-{{ cookiecutter.client_region }}/terraform.tfstate"
    dynamodb_table = "terraform-state"
    role_arn       = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
    session_name   = "{{ cookiecutter.client_acronym }}-{{ cookiecutter.client_region }}"
  }

  required_providers {
    bitbucket = {
      source = "zahiar/bitbucket"
    }
  }
}
