
provider "aws" {
  region = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::743401532020:role/AdminAccess"
    session_name = "fingermark-terraform-stb"
  }
}


provider "aws" {
  alias  = "usw1"
  region = "us-west-1"
  assume_role {
    role_arn     = "arn:aws:iam::743401532020:role/AdminAccess"
    session_name = "fingermark-terraform-stb"
  }
}

provider "aws" {
  alias  = "us-east-1"
  region = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::743401532020:role/AdminAccess"
    session_name = "fingermark-terraform-stb"
  }
}

provider "aws" {
  alias  = "us-east-2"
  region = "us-east-2"
  assume_role {
    role_arn     = "arn:aws:iam::743401532020:role/AdminAccess"
    session_name = "fingermark-terraform-stb"
  }
}

provider "vault" {
  address = "https://central.infra.fingermark.tech/vault"
}
