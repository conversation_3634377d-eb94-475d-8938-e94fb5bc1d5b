
resource "aws_iot_topic_rule" "roi_eyecue_event_kinesis" {
  name        = "roi_eyecue_event_kinesis"
  enabled     = true
  sql_version = "2016-03-23"
  description = "Topic Rule for Forwarding IoT ROI events to Kinesis Data Stream"

  sql  = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'roi' as event_type, '${var.client_name_kinesis}' as client_name FROM '/eyeq/roievent/#'"
  tags = var.tags
  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_log_group.name
    role_arn       = aws_iam_role.eyecue_iot_event_kinesis_role.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_event_kinesis_role.arn
    stream_name   = var.kinesis_stream_name_roi
    partition_key = "$${newuuid()}"
  }
}

resource "aws_iot_topic_rule" "hvi_eyecue_event_kinesis" {
  name        = "hvi_eyecue_event_kinesis"
  enabled     = true
  sql_version = "2016-03-23"
  description = "Topic Rule for Forwarding IoT HVI events to Kinesis Data Stream"

  sql  = "SELECT *, regexp_substr(camera_id, '^(\\w+-\\w+-\\w+-\\d+)') as store_id, 'hvi' as event_type, '${var.client_name_kinesis}' as client_name FROM '/eyeq/hci/#'"
  tags = var.tags
  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_log_group.name
    role_arn       = aws_iam_role.eyecue_iot_event_kinesis_role.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_event_kinesis_role.arn
    stream_name   = var.kinesis_stream_name_hvi
    partition_key = "$${newuuid()}"
  }
}

resource "aws_iot_topic_rule" "aggregated_eyecue_event_kinesis" {
  name        = "aggregated_eyecue_event_kinesis"
  enabled     = true
  sql_version = "2016-03-23"
  description = "Topic Rule for Forwarding IoT Aggregated events to Kinesis Data Stream"

  sql  = "SELECT *, site_id as store_id, 'aggregate' as event_type, '${var.client_name_kinesis}' as client_name FROM '/eyeq/vehicle-aggregated-2-0/#'"
  tags = var.tags
  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_log_group.name
    role_arn       = aws_iam_role.eyecue_iot_event_kinesis_role.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_event_kinesis_role.arn
    stream_name   = var.kinesis_stream_name_aggregate
    partition_key = "$${newuuid()}"
  }
}

resource "aws_iot_topic_rule" "departure_eyecue_event_kinesis" {
  name        = "departure_eyecue_event_kinesis"
  enabled     = true
  sql_version = "2016-03-23"
  description = "Topic Rule for Forwarding IoT Departure events to Kinesis Data Stream"

  sql  = "SELECT *, site_id as store_id, 'departure' as event_type, '${var.client_name_kinesis}' as client_name FROM '/eyeq/departure/#'"
  tags = var.tags
  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_log_group.name
    role_arn       = aws_iam_role.eyecue_iot_event_kinesis_role.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_event_kinesis_role.arn
    stream_name   = var.kinesis_stream_name_departure
    partition_key = "$${newuuid()}"
  }
}

resource "aws_iot_topic_rule" "arrival_eyecue_event_kinesis" {
  count       = var.kinesis_stream_name_arrival != "" ? 1 : 0
  name        = "arrival_eyecue_event_kinesis"
  enabled     = true
  sql_version = "2016-03-23"
  description = "Topic Rule for Forwarding IoT Arrival events to Kinesis Data Stream"

  sql  = "SELECT *, arrival.site_id as store_id, 'arrival' as event_type, '${var.client_name_kinesis}' as client_name FROM '/eyeq/arrival/#'"
  tags = var.tags
  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_log_group.name
    role_arn       = aws_iam_role.eyecue_iot_event_kinesis_role.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_event_kinesis_role.arn
    stream_name   = var.kinesis_stream_name_arrival
    partition_key = "$${newuuid()}"
  }
}

resource "aws_iot_topic_rule" "danger_zone_eyecue_event_kinesis" {
  count       = var.kinesis_stream_name_danger_zone != "" ? 1 : 0
  name        = "danger_zone_eyecue_event_kinesis"
  enabled     = true
  sql_version = "2016-03-23"
  description = "Topic Rule for Forwarding IoT Danger Zone events to Kinesis Data Stream"

  sql  = "SELECT *, topic(3) as store_id, 'danger-zone' as event_type, '${var.client_name_kinesis}' as client_name FROM '/eyeq/danger-zones/#'"
  tags = var.tags
  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.iot_log_group.name
    role_arn       = aws_iam_role.eyecue_iot_event_kinesis_role.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_event_kinesis_role.arn
    stream_name   = var.kinesis_stream_name_danger_zone
    partition_key = "$${newuuid()}"
  }
}

resource "aws_iam_role" "eyecue_iot_event_kinesis_role" {
  name = "eyecue-iot-event-kinesis-role"
  tags = var.tags
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "iot.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "eyecue_iot_event_kinesis_policy" {
  name        = "eyecue-iot-event-kinesis-policy"
  description = "Kinesis access policy for IoT Rule"
  tags        = var.tags
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "kinesis:PutRecord",
          "kinesis:PutRecords"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:kinesis:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:stream/ds-${var.client_acronym}-eyecue-*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eyecue_iot_event_kinesis_role_attachment" {
  policy_arn = aws_iam_policy.eyecue_iot_event_kinesis_policy.arn
  role       = aws_iam_role.eyecue_iot_event_kinesis_role.name
}

resource "aws_cloudwatch_log_group" "iot_log_group" {
  name              = "/aws/iot/topicRule/eyecue_event_kinesis"
  retention_in_days = 1
  tags              = var.tags
}

resource "aws_iam_policy" "eyecue_iot_event_kinesis_cloudwatch_logs_policy" {
  name        = "eyecue-iot-event-kinesis-cloudwatch-logs-policy"
  description = "CloudWatch Logs access policy for IoT Rule"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      Effect   = "Allow",
      Resource = "*"
    }]
  })
}

resource "aws_iam_policy_attachment" "eyecue_iot_event_kinesis_cloudwatch_logs_policy_attachment" {
  name       = "eyecue-iot-event-kinesis-cloudwatch-logs-attachment"
  policy_arn = aws_iam_policy.eyecue_iot_event_kinesis_cloudwatch_logs_policy.arn
  roles      = [aws_iam_role.eyecue_iot_event_kinesis_role.name]
}
