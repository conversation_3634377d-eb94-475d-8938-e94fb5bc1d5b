# Fingermark Terraform

This repository manages the infrastructure for different accounts across all company. Every customer account should be under a project folder. 

### Requirements

- AdminAccess role on the target account
- TerraformBackendAccess role on the infra account (for manipulating the state file)
- python3
- Optional: fingermark keybase account configured on your PC. This is for fetching the IAM users credentials

### Adding a customer

To add a new production customer account you can utilise the cookiecutter template.

```shell
pip install cookiecutter
cd fingermark/eyecue/prod
cookiecutter %customer-template%
# then follow instructions provided by cookiecutter / README
```

### Terraform Cheatsheet

You have to initialize Terraform by running

```shell
terraform init
```

Check you are on a clean state by running

```shell
terraform plan -out=plan
```

After running this last command, you should see no changes

If you do which to apply changes run:

```shell
terraform apply plan
```

### Generate output
Need to generate output file in json format

```shell
terraform output -json > output.json
```

### Getting the IAM credentials

```shell
python3 get_credentials.py <project> <customer>
```

The script will ask for the fingermark keybase account password
