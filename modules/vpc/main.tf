resource "aws_vpc" "default" {
  cidr_block           = var.vpc_cidr_block
  instance_tenancy     = "default"
  enable_dns_support   = "true"
  enable_dns_hostnames = "true"
  enable_classiclink   = "false"
  tags = merge(var.tags,
    { Name = "${var.vpc_tag_name}" }
  )
}

resource "aws_subnet" "main_subnet_public" {
  count                   = 2
  vpc_id                  = aws_vpc.default.id
  cidr_block              = cidrsubnet(var.vpc_cidr_block, 4, count.index)
  map_public_ip_on_launch = "true"
  availability_zone       = data.aws_availability_zones.this.names[count.index]

  tags = merge(var.tags,
    { Name = "${var.vpc_tag_name}VpcSubnet${count.index}" }
  )

  lifecycle {
    ignore_changes = [
      availability_zone,
      cidr_block
    ]
  }
}

resource "aws_internet_gateway" "main_gw" {
  vpc_id = aws_vpc.default.id
  tags = merge(var.tags,
    { Name = "${var.vpc_tag_name}GW" }
  )
}

# Route tables
resource "aws_route_table" "main_public" {
  vpc_id = aws_vpc.default.id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main_gw.id
  }
  tags = merge(var.tags,
    { Name = "${var.vpc_tag_name}PublicRouteTable" }
  )
}

resource "aws_main_route_table_association" "a" {
  vpc_id         = aws_vpc.default.id
  route_table_id = aws_route_table.main_public.id
}

# Security Groups
resource "aws_security_group" "main_sec_group" {
  vpc_id = aws_vpc.default.id
  name   = "postgressg"
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port = 5432
    to_port   = 5432
    protocol  = "tcp"
    # cidr_blocks = [aws_vpc.default.cidr_block]
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = merge(var.tags,
    { Name = "${var.vpc_tag_name}SecGroup" }
  )
}

resource "aws_security_group" "havelock_north_access" {
  name        = "HavelockNorthAccess"
  description = "Allow full access from HN office"
  vpc_id      = aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["**************/32"]
  }

  tags = var.tags
}