locals {
  lambda_zip = "${path.module}/lambda_function/lambda_function.zip"
}

locals {
  policy_1 = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "kinesis:PutRecord",
          "kinesis:PutRecords"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:kinesis:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:stream/ds-${var.client_acronym}-eyecue-*"
        ]
      },
      {
        Action = [
          "kms:Decrypt",
          "kms:encrypt",
          "kms:DescribeKey",
          "kms:GenerateDataKey"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })

}

locals {
  policy_2 = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "kinesis:PutRecord",
          "kinesis:PutRecords"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:kinesis:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:stream/ds-${var.client_acronym}-eyecue-*"
        ]
      }
    ]
  })
}

resource "aws_iot_topic_rule" "eyecue_iot_kinesis_eventstream" {
  for_each    = var.kinesis_iot_topic_rules_config
  name        = each.value["name"]
  enabled     = each.value["enabled"]
  sql_version = "2016-03-23"
  description = each.value["description"]
  tags        = var.tags
  sql         = each.value["sql"]

  cloudwatch_logs {
    log_group_name = aws_cloudwatch_log_group.eyecue_iot_kinesis_eventstream.name
    role_arn       = aws_iam_role.eyecue_iot_kinesis_eventstream.arn
  }

  kinesis {
    role_arn      = aws_iam_role.eyecue_iot_kinesis_eventstream.arn
    stream_name   = each.value["stream_name"]
    partition_key = "$${newuuid()}"
  }

  dynamic "error_action" {
    for_each = var.enable_fallback_to_s3 ? [1] : []

    content {
      lambda {
        function_arn = aws_lambda_function.eyecue_iot_kinesis_fallback_lambda_handler[0].arn
      }
    }
  }
}

resource "aws_iam_role" "eyecue_iot_kinesis_eventstream" {
  name = var.iot_kinesis_eventstream_iam_role_name
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "iot.amazonaws.com"
        }
      }
    ]
  })
  tags = var.tags
}

resource "aws_iam_policy" "eyecue_iot_kinesis_eventstream_kinesis" {
  name        = var.iot_kinesis_eventstream_iam_policy_name
  description = "Kinesis access policy for IoT Rule"
  policy      = var.is_data_sharing_enabled ? local.policy_1 : local.policy_2
}

resource "aws_iam_role_policy_attachment" "eyecue_iot_kinesis_eventstream" {
  policy_arn = aws_iam_policy.eyecue_iot_kinesis_eventstream_kinesis.arn
  role       = aws_iam_role.eyecue_iot_kinesis_eventstream.name
}

resource "aws_cloudwatch_log_group" "eyecue_iot_kinesis_eventstream" {
  name              = "/aws/iot/topicRule/${var.iot_kinesis_cloudwatch_log_group_name}"
  retention_in_days = 1
  tags              = var.tags
}


resource "aws_iam_policy" "eyecue_iot_kinesis_eventstream_cloudwatch" {
  name        = var.iot_kinesis_cloudwatch_log_group_iam_policy_name
  description = "CloudWatch Logs access policy for IoT Rule"
  tags        = var.tags
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      Effect   = "Allow",
      Resource = "*"
    }]
  })
}

resource "aws_iam_policy_attachment" "eyecue_iot_kinesis_eventstream" {
  name       = var.iot_kinesis_eventstream_iam_policy_attachment_name
  policy_arn = aws_iam_policy.eyecue_iot_kinesis_eventstream_cloudwatch.arn
  roles      = [aws_iam_role.eyecue_iot_kinesis_eventstream.name]
}

resource "aws_iam_role" "eyecue_iot_kinesis_fallback_lambda_role" {
  count = var.enable_fallback_to_s3 ? 1 : 0
  name  = var.iot_kinesis_fallback_lambda_iam_role_name
  tags  = var.tags
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "eyecue_iot_kinesis_fallback_lambda_policy" {
  count       = var.enable_fallback_to_s3 ? 1 : 0
  name        = var.iot_kinesis_fallback_lambda_iam_policy_name
  description = "Policy to allow Lambda function to write to S3 and be invoked by IoT"
  tags        = var.tags
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl"
        ],
        Effect   = "Allow",
        Resource = "arn:aws:s3:::${var.fallback_bucket_name}/*"
      },
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Effect   = "Allow",
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eyecue_iot_kinesis_fallback_lambda_policy_attachment" {
  count      = var.enable_fallback_to_s3 ? 1 : 0
  role       = aws_iam_role.eyecue_iot_kinesis_fallback_lambda_role[count.index].name
  policy_arn = aws_iam_policy.eyecue_iot_kinesis_fallback_lambda_policy[count.index].arn
}

resource "aws_lambda_function" "eyecue_iot_kinesis_fallback_lambda_handler" {
  count            = var.enable_fallback_to_s3 ? 1 : 0
  function_name    = var.iot_kinesis_fallback_lambda_function_name
  role             = aws_iam_role.eyecue_iot_kinesis_fallback_lambda_role[count.index].arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.8"
  filename         = local.lambda_zip
  source_code_hash = filebase64sha256(local.lambda_zip)
  tags             = var.tags
  environment {
    variables = {
      CLIENT_NAME = var.client_name
      BUCKET_NAME = var.fallback_bucket_name
    }
  }
}

resource "aws_lambda_permission" "iot_invoke_lambda" {
  count         = var.enable_fallback_to_s3 ? 1 : 0
  statement_id  = "AllowIoTInvokeLambda"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.eyecue_iot_kinesis_fallback_lambda_handler[count.index].function_name
  principal     = "iot.amazonaws.com"
}
