module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "common" {
  source                = "../../../../modules/common"
  aws_account_id        = data.aws_caller_identity.current.account_id
  aws_region            = var.AWS_REGION
  client_name           = var.CLIENT_NAME
  client_acronym        = var.CLIENT_ACRONYM
  country               = var.COUNTRY
  country_full          = var.COUNTRY_FULL
  aws_iam_roles         = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DbaAccess"]
  keybase               = var.KEYBASE
  cloudcraft_access     = true
  env                   = var.ENVIRONMENT
  grafana_iam_user_list = ["grafana", "elj-grafana"]
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "vanta" {
  source = "../../../../modules/vanta"
}

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "eyecue_rds" {
  source                        = "../../../../modules/eyecue_rds"
  create_replica                = false
  rds_apply_changes_immediately = true
  rds_master_instance_class     = "db.t3.small"
  # rds_master_instance_class     = var.RDS_MASTER_INSTANCE_CLASS
  rds_engine_version            = "16.3"
  rds_ca_cert_identifier        = "rds-ca-rsa2048-g1"
  special_password              = false
  product                       = "Eyecue"
  vpc_id                        = module.eyecue_network.vpc_id
  aws_region                    = var.AWS_REGION
  aws_account_id                = data.aws_caller_identity.current.account_id
  vpc_security_group_ids        = [module.eyecue_network.havelock_security_group_id]
  subnet_ids                    = module.eyecue_network.public_subnet_ids
  rds_allocated_storage         = 200
  rds_max_allocated_storage     = 1000
  eyecue_rds_cloudflare_api_key = data.vault_generic_secret.cloudflare.data["api_key"]
  eyecue_rds_customer_id        = var.CLIENT_ACRONYM
  create_db_parameter_group     = true
  parameter_group_family        = "postgres16"
  parameter_group_parameters = [ # Explicitly disable forced SSL
    {
      name  = "rds.force_ssl"
      value = "0"
    }
  ]
  allow_major_version_upgrade = true
}

module "secret_manager" {
  source                              = "../../../../modules/secret_manager"
  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"
  eyecue_dashboard_data_secret_name   = "rds/ssm/eyecue-dashboard-data"
}

module "icinga2_satellite" {
  source                                         = "../../../../modules/icinga2_satellite"
  icinga2_satellite_vpc_id                       = module.eyecue_network.vpc_id
  icinga2_satellite_ec2_ami_id                   = "ami-0b7dcd6e6fd797935"
  icinga2_satellite_ec2_subnet_id                = module.eyecue_network.public_subnet_ids[1]
  icinga2_satellite_ec2_extra_security_group_ids = [module.eyecue_network.havelock_security_group_id]
  icinga2_satellite_customer_id                  = var.CLIENT_ACRONYM
  icinga2_satellite_ec2_ssh_key_name             = "matias"
  icinga2_satellite_cloudflare_api_key           = data.vault_generic_secret.cloudflare.data["api_key"]
}

module "eyecue_mimir" {
  source                   = "../../../../modules/eyecue_mimir"
  mimir_aws_account_id     = "************"
  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "Policy to invoke request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}

module "eyecue_customer_edw_integration" {
  source                  = "../../../../modules/eyecue_customer_edw_integration"
  client_name             = var.CLIENT_NAME
  country                 = var.COUNTRY
  bucket_name_reference   = "edw-integration"
  keybase                 = var.KEYBASE
  customer_aws_account_id = "************"
}

resource "aws_security_group" "elj_grafana_sg" {
  name        = "elj-grafana-rds"
  description = "Allow inbound traffic onto rds from fargate"
  vpc_id      = "vpc-07b43ef3be9e2e893"

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
}

resource "aws_security_group_rule" "elj_grafana_sg_rule" {
  description              = "Allow inbound traffic onto aurora"
  type                     = "ingress"
  security_group_id        = aws_security_group.elj_grafana_sg.id
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = module.grafana_server.grafana_web_security_group_id
}

module "grafana_server" {
  grafana_iam_user_name        = "elj-grafana"
  grafana_iam_user_name_list   = ["grafana", "elj-grafana"]
  source                       = "../../../../modules/grafana_server"
  vpc_id                       = "vpc-07b43ef3be9e2e893"
  grafana_db_engine_version    = "2.11.4"
  create_db_subnet_group       = true
  grafana_db_subnet_group_name = "grafana-rds-subnet-group"
  grafana_db_subnet_ids        = module.eyecue_network.private_subnet_ids
  grafana_db_allowed_sec_groups = [
    aws_security_group.elj_grafana_sg.id
  ]
  grafana_db_vpc_sec_group_ids = [
    aws_security_group.elj_grafana_sg.id
  ]
  grafana_web_image_tag                      = "11.5.2"
  grafana_web_env_vars                       = local.grafana_web_env_vars
  grafana_web_vpc_id                         = "vpc-07b43ef3be9e2e893"
  grafana_web_subnet_ids                     = module.eyecue_network.private_subnet_ids
  grafana_web_create_alb                     = true
  grafana_alb_subnet_ids                     = module.eyecue_network.public_subnet_ids
  grafana_web_target_group_health_check_path = "/login"
  grafana_web_security_group_ids             = ["sg-0f0f96a5a9a2e159c"]
  create_attachment_policy                   = false
}

resource "aws_acm_certificate" "grafana_server_alb_cert" {
  domain_name       = "grafana.eljannah.eyecue.fingermark.co.nz"
  validation_method = "DNS"

  tags = merge(var.tags, var.default_tags)

  lifecycle {
    create_before_destroy = true
  }
}

module "grafana_subdomain_validation" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_record_name  = tolist(aws_acm_certificate.grafana_server_alb_cert.domain_validation_options)[0].resource_record_name
  cloudflare_record_value = tolist(aws_acm_certificate.grafana_server_alb_cert.domain_validation_options)[0].resource_record_value
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = tolist(aws_acm_certificate.grafana_server_alb_cert.domain_validation_options)[0].resource_record_type
  cloudflare_zone_id      = "13bbaa28a85416bdd354f6014cdac2e3"
}

resource "aws_acm_certificate_validation" "grafana_subdomain_certificate_validation" {
  certificate_arn         = aws_acm_certificate.grafana_server_alb_cert.arn
  validation_record_fqdns = [module.grafana_subdomain_validation.hostname]
}

resource "aws_lb_listener" "grafana_alb_https_listener" {
  load_balancer_arn = module.grafana_server.grafana_alb_arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = aws_acm_certificate.grafana_server_alb_cert.arn

  default_action {
    type             = "forward"
    target_group_arn = module.grafana_server.grafana_alb_target_group_arn
  }

  depends_on = [
    resource.aws_acm_certificate_validation.grafana_subdomain_certificate_validation
  ]
}

module "grafana_subdomain" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_record_name  = "grafana.eljannah.eyecue"
  cloudflare_record_value = module.grafana_server.grafana_alb_dns_name
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
  cloudflare_zone_id      = "13bbaa28a85416bdd354f6014cdac2e3"
}

module "kinesis_common_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_NAME
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.retention_period
  stream_mode                    = var.stream_mode
  stream_name_list               = var.stream_name_list
  create_role                    = true
  current_account_id             = data.aws_caller_identity.current.account_id
}

module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = var.CLIENT_ACRONYM
  kinesis_iot_topic_rules_config = var.kinesis_iot_topic_rules_config
  fallback_bucket_name           = "fm-data-eyecue-kinesis-failure-ap-southeast-2"
  enable_fallback_to_s3          = true
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(

          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
    }
  }
  elb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        alb_config = {
          "GrafanaALB" = { lb_name = "GrafanaALB", tg_names = ["GrafanaALB"] }
        }
      }
    }
  }
}

module "rds_cw_alarms" {
  source                                 = "../../../../modules/rds_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_rds_cpu_util           = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_mem_free           = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_disk_queue_depth   = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_write_iops         = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_read_iops          = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_free_storage_space = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  tags                                   = var.tags
  default_tags                           = var.default_tags
}

module "dynamodb_cw_alarms_defaults_ap_southeast_2" {
  source                                 = "../../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config

  tags         = var.tags
  default_tags = var.default_tags
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "icinga2-satellite" = { instance_tags = { Name = "icinga2-satellite" } }
    "wireguard"         = { instance_tags = { Name = "wireguard" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

module "elb_cw_alarms_ap_southeast_2" {
  source                                        = "../../../../modules/elb_cw_alarms"
  sns_topic_arns                                = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_alb_healthy_host_count        = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_unhealthy_host_count      = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_target_response_time      = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_httpcode_elb_5xx_count    = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  cw_alarm_config_alb_httpcode_target_5xx_count = local.elb_cw_alarms.defaults.ap_southeast_2.alb_config
  tags                                          = var.tags
  default_tags                                  = var.default_tags
}

module "pentest_iam_user" {
  source   = "../../../../modules/pentest_iam_user"
  username = "eyecue-pentest-user"
  tags = {
    Purpose     = "Annual Penetration Testing"
    Temporary   = "true"
    Environment = "Pentest"
    Squad       = "Platform team"
  }
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
