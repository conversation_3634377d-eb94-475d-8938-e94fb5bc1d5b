variable "kubeiam_role" {
  description = "Kubeiam IAM role name"
  type        = string
  default     = ""
}

variable "worker_node_arn" {
  description = "Worker node ARN for policy"
  type        = string
  default     = ""
}

variable "kubeiam_policy" {
  description = "Kubeiam policy"
  type        = string
  default     = ""
}

variable "kubeiam_trustpolicy" {
  description = "Kubeiam Trust policy"
  type        = string
  default     = ""
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "Security"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}