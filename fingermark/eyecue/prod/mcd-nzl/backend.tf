terraform {
  required_version = "1.7.2"
  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "ap-southeast-2"
    key            = "mnz/terraform.tfstate"
    dynamodb_table = "terraform-state"
    role_arn       = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
    session_name   = "infra"
  }
  required_providers {
    local = {
      version = "~> 2.1"
    }
  }
}
