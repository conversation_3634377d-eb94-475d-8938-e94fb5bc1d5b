terraform {
  required_version = "1.7.2"
  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "ap-southeast-2"
    key            = "stg-nzl/terraform.tfstate"
    dynamodb_table = "terraform-state"
    role_arn       = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
    session_name   = "stg-nzl"
  }

  required_providers {
    bitbucket = {
      source = "zahiar/bitbucket"
    }
  }
}
