module "grafana_user" {
  source                   = "../grafana"
  aws_iam_user             = var.grafana_iam_user_name
  aws_iam_user_list        = var.grafana_iam_user_name_list
  create_attachment_policy = var.create_attachment_policy
}

resource "random_string" "grafana_db_master_password" {
  length  = 32
  special = false
  lifecycle {
    ignore_changes = [
      length, lower, min_lower, min_numeric, min_special, min_upper, numeric, special, upper
    ]
  }
}

data "aws_iam_policy" "CloudWatchAgentServerPolicy" {
  arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

resource "aws_iam_role" "CloudWatchAgentServerRole" {
  name               = "CloudWatchAgentServerRole"
  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": "sts:AssumeRole",
            "Principal": {
               "Service": "ec2.amazonaws.com"
            },
            "Effect": "Allow",
            "Sid": ""
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "CloudWatchAgentServerPolicyAttachment" {
  role       = aws_iam_role.CloudWatchAgentServerRole.name
  policy_arn = data.aws_iam_policy.CloudWatchAgentServerPolicy.arn
}

resource "aws_iam_instance_profile" "CloudWatchAgentServerRole" {
  name = "CloudWatchAgentServerRole"
  role = aws_iam_role.CloudWatchAgentServerRole.name
}

module "grafana_db" {
  # https://registry.terraform.io/modules/terraform-aws-modules/rds-aurora/aws/latest
  source  = "terraform-aws-modules/rds-aurora/aws"
  version = "6.2.0"

  name           = "grafana"
  engine         = var.grafana_db_engine
  engine_version = var.grafana_db_engine_version
  engine_mode    = "serverless"
  # replica_count       = 0
  deletion_protection = true

  database_name   = var.grafana_db_schema_name
  master_username = var.grafana_db_user_name
  master_password = random_string.grafana_db_master_password.result

  vpc_id                  = var.vpc_id
  create_db_subnet_group  = var.create_db_subnet_group
  db_subnet_group_name    = var.grafana_db_subnet_group_name
  subnets                 = var.grafana_db_subnet_ids
  allowed_security_groups = var.grafana_db_allowed_sec_groups
  vpc_security_group_ids  = var.grafana_db_vpc_sec_group_ids
  create_security_group   = false

  scaling_configuration = var.grafana_db_scaling_config

  tags = merge(var.default_tags, var.tags)
}

module "grafana_web" {
  source                   = "../fargate"
  fargate_application_name = "Grafana"
  container_image          = "${var.grafana_web_image_name}:${var.grafana_web_image_tag}"
  container_port           = var.grafana_web_container_port
  env_vars                 = var.grafana_web_env_vars

  vpc_id                                     = var.grafana_web_vpc_id
  alb_subnets_ids                            = var.grafana_alb_subnet_ids
  fargate_subnets_ids                        = var.grafana_web_subnet_ids
  tags                                       = merge(var.default_tags, var.tags)
  create_alb                                 = var.grafana_web_create_alb
  external_alb_security_group_id             = var.grafana_web_external_sec_group
  external_alb_id                            = var.grafana_web_external_alb_arn
  fargate_app_target_group_health_check_path = var.grafana_web_target_group_health_check_path
  fargate_app_security_group_ids             = var.grafana_web_security_group_ids
  fargate_app_ecs_service_desired_count      = var.grafana_web_ecs_service_desired_count
  ecs_task_cpu                               = var.grafana_web_ecs_task_cpu
  ecs_task_memory                            = var.grafana_web_ecs_task_memory
}
