
AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "cv-qa"
CLIENT_ACRONYM = ""
COUNTRY        = "au"
# RDS_MASTER_INSTANCE_CLASS = "db.t3.micro"
ENVIRONMENT = "dev"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "argus-connections"                       = { table_name = "argus-connections" },
  "argus-messages"                          = { table_name = "argus-messages" },
  "eyecue-benchmarking-cases"               = { table_name = "eyecue-benchmarking-cases" },
  "eyecue-data-capture"                     = { table_name = "eyecue-data-capture" },
  "eyecue-deployer-params-for-benchmarking" = { table_name = "eyecue-deployer-params-for-benchmarking" },
  "eyecue-helm-values"                      = { table_name = "eyecue-helm-values" },
  "eyecue-parameters-schema"                = { table_name = "eyecue-parameters-schema" },
  "eyecue-sites-configuration"              = { table_name = "eyecue-sites-configuration" },
  "eyecue-sites-container"                  = { table_name = "eyecue-sites-container" },
  "eyecue-things-shadow"                    = { table_name = "eyecue-things-shadow" },
  "eyecue-weights"                          = { table_name = "eyecue-weights" },
  "eyeq-benchmarker-deployments-table"      = { table_name = "eyeq-benchmarker-deployments-table" },
  "eyeq-benchmarker-sites-table"            = { table_name = "eyeq-benchmarker-sites-table" },
  "eyeq-benchmarker-table"                  = { table_name = "eyeq-benchmarker-table" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "serverless-rest-api-with-dynamodb-dev" = { table_name = "serverless-rest-api-with-dynamodb-dev" }
}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {}
