
terraform {
  required_version = "1.7.2"
  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "ap-southeast-2"
    key            = "francium/terraform.tfstate"
    dynamodb_table = "terraform-state"
    role_arn       = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
    session_name   = "default"
  }
  # cloud {
  #   organization = "Fingermark"
  #   workspaces {
  #     name = "infra-francium"
  #   }
  # }
  required_providers {
    local = {
      version = "~> 2.1"
    }
  }
}
