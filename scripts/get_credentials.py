#!.venv/bin/python3

import json
import subprocess
import re
import argparse
import os
from loguru import logger

parser = argparse.ArgumentParser(description='Python script for getting IAM credentials. The script will present found credentials on screen')
parser.add_argument('project', help='Indicate the project name: eyecue/mining')
parser.add_argument('customer', help='Indicate the customer acronym: qa/mcd/kfc')
args = parser.parse_args()
project = args.project
current_dir = os.getcwd()
project_path = f'{current_dir}/../fingermark/{project}/{args.customer}'

def decrypt_key(encrypted_key):
    p1 = subprocess.Popen(["echo", encrypted_key], stdout=subprocess.PIPE)
    p2 = subprocess.Popen( ["base64","--decode"], stdin=p1.stdout, stdout=subprocess.PIPE)
    key = subprocess.Popen( ["keybase", "pgp", "decrypt"], stdin=p2.stdout, stdout=subprocess.PIPE)
    (output, err) = key.communicate()  
    return output.decode()

def base64_encode(key):
    key = subprocess.Popen(["echo", str(key)], stdout=subprocess.PIPE)
    encrypted_key = subprocess.Popen( ["base64"], stdin=key.stdout, stdout=subprocess.PIPE)
    (output, err) = encrypted_key.communicate()
    return(output.decode())


results = []

with open(project_path + "/output.json", "r") as output_file:
    out = json.loads(output_file.read())


for user, values in out.items():
    if 'arn' in values["value"]:
        if 'user' in values["value"]["arn"]:
            if values["value"]["encrypted_secret_key"] == None:
                logger.warning(f'{user} has a null secret key, it won\'t be included :(')
            else:
                try:
                    secret_key = decrypt_key(str(values["value"]["encrypted_secret_key"]))
                except Exception as e:
                    logger.error(f"{e}")
                if secret_key != "":
                    try:
                        base64_access_key = re.sub(r"K$", "=", base64_encode(values["value"]["access_key"]).replace("\n", ""))
                        base64_secret_key = re.sub(r"o=$", "==", base64_encode(secret_key).replace("\n", ""))
                        results.append(
                            {
                                "name": user,
                                "access_key": values["value"]["access_key"],
                                "base64_access_key": base64_access_key,
                                "secret_key": secret_key,
                                "base64_secret_key": base64_secret_key
                            }
                        )
                        logger.debug(f'{values["value"]["name"]}:::{values["value"]["access_key"]}:::{secret_key}')
                    except Exception as e:
                        logger.error(f"{e}")

with open(project_path + "/credentials.json", "w+") as credentials_file:
    credentials_file.write(json.dumps(results))
    