variable "vpc_name" {
  type        = string
  description = "VPC name"
}

variable "vpc_cidr_block" {
  type        = string
  description = "CIDR block for the VPC."
  default     = "10.0.0.0/16"

  validation {
    condition     = can(regex("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(1[6-9]|2[0-8]))$", var.vpc_cidr_block))
    error_message = "CIDR block parameter must be in the form x.x.x.x/16-28."
  }
}

variable "private_subnets" {
  description = "A list of private subnets inside the VPC"
  type        = list(string)
  default     = []
}

variable "public_subnets" {
  description = "A list of public subnets inside the VPC"
  type        = list(string)
  default     = []
}

variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "azs" {
  description = "A list of availability zones names or ids in the region"
  type        = list(string)
  default     = []
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "vpc_tags" {
  type        = any
  description = "a group of tags to tag vpc"
  default = {
    Terraform   = "true"
    Environment = "prod"
    Stack       = "cv"
    Product     = "Eyecue"
    Squad       = "Platform"
  }
}

variable "public_subnet_tags" {
  type        = any
  description = "a group of tags to tag vpc"
  default     = {}
}

variable "private_subnet_tags" {
  type        = any
  description = "a group of tags to tag private vpc"
  default     = {}
}

variable "havelocknorthaccess_ips" {
  type        = list(any)
  description = "List of HavelockNorthAccess IPs"
  default     = ["**************/32"]
}

variable "vpn_ips" {
  type        = list(any)
  description = "List of VPN IPs that are going to be whitelisted"
  default     = ["**************/32"]
}

variable "havelocknorthaccess_sg" {
  type        = string
  description = "Trigger for when to create the HavelockNorthAccess SG"
  default     = "disable"
}

variable "allow_additional_routes" {
  description = "Flag to allow additional routes to the route table"
  type        = bool
  default     = false
}

variable "additional_routes" {
  description = "A map of CIDR blocks to ENI IDs or other route targets"
  type        = map(string)
  default     = {}
}
