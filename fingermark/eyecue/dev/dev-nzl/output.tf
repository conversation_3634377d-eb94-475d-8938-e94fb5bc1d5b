output "eyecue_dashboard_iot_user" {
  value     = module.common.eyecue_dashboard_iot_user
  sensitive = true
}

output "eyecue_images_user" {
  value     = module.common.eyecue_images_user
  sensitive = true
}

output "eyecue_iot_user" {
  value     = module.common.eyecue_iot_user
  sensitive = true
}

output "eyecue_logs_user" {
  value     = module.common.eyecue_logs_user
  sensitive = true
}

output "eyecue_mosaic_user" {
  value     = module.common.eyecue_mosaic_user
  sensitive = true
}

output "eyecue_roi_configurator_user" {
  value     = module.common.eyecue_roi_configurator_user
  sensitive = true
}

output "eyecue_server_user" {
  value     = module.common.eyecue_server_user
  sensitive = true
}

output "eyecue_weights_user" {
  value     = module.common.eyecue_weights_user
  sensitive = true
}

output "eyecue_grafana_user" {
  value     = module.common.eyecue_grafana_user
  sensitive = true
}

output "eyecue_roi_suggestor_user" {
  value     = module.common.eyecue_roi_suggestor_user
  sensitive = true
}