locals {
  accounts = {
    data_dev      = [for acct in data.aws_organizations_organizational_unit_child_accounts.data_dev.accounts : acct.id if acct.status == "ACTIVE"]
    data_prod     = [for acct in data.aws_organizations_organizational_unit_child_accounts.data_prod.accounts : acct.id if acct.status == "ACTIVE"]
    eyecue_dev    = [for acct in data.aws_organizations_organizational_unit_child_accounts.eyecue_dev.accounts : acct.id if acct.status == "ACTIVE"]
    eyecue_prod   = [for acct in data.aws_organizations_organizational_unit_descendant_accounts.eyecue_prod.accounts : acct.id if acct.status == "ACTIVE"]
    northvue_dev  = [for acct in data.aws_organizations_organizational_unit_child_accounts.northvue_dev.accounts : acct.id if acct.status == "ACTIVE"]
    northvue_stg  = [for acct in data.aws_organizations_organizational_unit_child_accounts.northvue_stg.accounts : acct.id if acct.status == "ACTIVE"]
    northvue_prod = [for acct in data.aws_organizations_organizational_unit_child_accounts.northvue_prod.accounts : acct.id if acct.status == "ACTIVE"]
    platform      = [for acct in data.aws_organizations_organizational_unit_child_accounts.platform.accounts : acct.id if acct.status == "ACTIVE"]
    fm_prod       = [for acct in data.aws_organizations_organizational_unit_child_accounts.fm_prod.accounts : acct.id if acct.status == "ACTIVE"]
    dmbo          = [for acct in data.aws_organizations_organizational_unit_child_accounts.dmbo.accounts : acct.id if acct.status == "ACTIVE"]
    supersonic    = [for acct in data.aws_organizations_organizational_unit_child_accounts.supersonic.accounts : acct.id if acct.status == "ACTIVE"]
    support       = [for acct in data.aws_organizations_organizational_unit_child_accounts.support.accounts : acct.id if acct.status == "ACTIVE"]

    cv_prod = [for acct in data.aws_organizations_organizational_unit_descendant_accounts.eyecue_prod.accounts : acct.id if can(regex("(?i)CV PROD", acct.name))]
  }

  permission_set_associations = [
    {
      name = "AWS Infra Team"
      permission_sets = [
        {
          name = "AdminAccess"
          account = concat(
            local.accounts.data_dev,
            local.accounts.data_prod,
            local.accounts.eyecue_dev,
            local.accounts.eyecue_prod,
            local.accounts.northvue_dev,
            local.accounts.northvue_stg,
            local.accounts.northvue_prod,
            local.accounts.platform,
            local.accounts.fm_prod,
            local.accounts.dmbo,
            local.accounts.supersonic,
            local.accounts.support,
          )
        },
        {
          name    = "KommisjonAccess"
          account = local.accounts.cv_prod
        },
      ]
    },
    {
      name = "AWS Eyecue Team"
      permission_sets = [
        {
          name = "PowerAccess"
          account = concat(
            local.accounts.eyecue_dev,
            local.accounts.northvue_dev,
            local.accounts.northvue_stg,
          )
        },
        {
          name = "DevAccess"
          account = concat(
            local.accounts.eyecue_dev,
            local.accounts.eyecue_prod,
            local.accounts.northvue_dev,
            local.accounts.northvue_stg,
            local.accounts.northvue_prod,
          )
        },
        {
          name    = "KommisjonAccess"
          account = local.accounts.cv_prod
        },
      ]
    },
    {
      name = "AWS Eyecue Admin"
      permission_sets = [
        {
          name = "AdminAccess"
          account = concat(
            local.accounts.eyecue_dev,
            local.accounts.eyecue_prod,
            local.accounts.northvue_dev,
            local.accounts.northvue_stg,
            local.accounts.northvue_prod,
          )
        }
      ]
    },
    {
      name = "AWS Eyecue Storage"
      permission_sets = [
        {
          name = "StorageAccess"
          account = [
            "************",
            "************",
            "************"
          ]
        }
      ]
    },
    {
      name = "AWS Supersonic Team"
      permission_sets = [
        {
          name    = "PowerAccess"
          account = []
        },
        {
          name = "DevAccess"
          account = concat(
            local.accounts.supersonic,
            local.accounts.fm_prod, # not sure if needed?
          )
        }
      ]
    },
    {
      name = "AWS Data Team"
      permission_sets = [
        {
          name    = "PowerAccess"
          account = local.accounts.data_dev
        },
        {
          name = "DataScientistAccess"
          account = concat(
            local.accounts.data_dev,
            local.accounts.data_prod,
            local.accounts.eyecue_prod,
          )
        },
        {
          name = "ReadOnlyAccess"
          account = concat(
            local.accounts.data_dev,
            local.accounts.data_prod,
            local.accounts.eyecue_prod,
            local.accounts.northvue_prod,
          )
        }
      ]
    },
    {
      name = "AWS Data Admin"
      permission_sets = [
        {
          name = "AdminAccess"
          account = concat(
            local.accounts.data_dev,
            local.accounts.data_prod,
          )
        }
      ]
    },
    {
      name = "AWS ReadOnly"
      permission_sets = [
        {
          name = "ReadOnlyAccess"
          account = concat(
            local.accounts.data_dev,
            local.accounts.data_prod,
            local.accounts.eyecue_dev,
            local.accounts.eyecue_prod,
            local.accounts.northvue_dev,
            local.accounts.northvue_stg,
            local.accounts.northvue_prod,
          )
        }
      ]
    },
    {
      name = "AWS Product"
      permission_sets = [
        {
          name    = "QuicksightAdminAccess"
          account = local.accounts.eyecue_prod
        }
      ]
    },
    {
      name = "AWS Support Team"
      permission_sets = [
        {
          name    = "SupportAccess"
          account = local.accounts.support
        },
        {
          name    = "KommisjonPortForwardAccess"
          account = local.accounts.cv_prod
        },
      ]
    },
    {
      name = "AWS ICXeed Team"
      permission_sets = [
        {
          name    = "ICXeedAccess"
          account = local.accounts.support
        },
        {
          name    = "AdminAccess"
          account = local.accounts.support
        }
      ]
    },
  ]
}
