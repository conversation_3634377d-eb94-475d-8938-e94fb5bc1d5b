##################
# SRE 378
##################

resource "aws_iam_policy" "infra_slacker_lambda_access_to_sqs" {
  name = "${var.application_name}LambdaAccessToSQS"
  path = "/${var.stage}/${var.application_name}/"
  policy = jsonencode(
    {
      "Version" = "2012-10-17"
      "Statement" = [
        {
          "Sid"    = "VisualEditor0",
          "Effect" = "Allow",
          "Action" = [
            "sqs:DeleteMessage",
            "logs:CreateLogStream",
            "sqs:ReceiveMessage",
            "sqs:GetQueueAttributes",
            "logs:CreateLogGroup",
            "logs:PutLogEvents"
          ],
          "Resource" = [
            "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.application_name}:*:*",
            "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.application_name}:*",
            "arn:aws:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.application_name}"
          ]
        }
      ]
  })

}

resource "aws_iam_role" "infra_slacker_lambda_access_to_sqs" {
  name = "${var.application_name}LambdaAccessToSQS"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}


module "lambda_function_infra_slacker" {
  source = "terraform-aws-modules/lambda/aws"

  function_name           = var.application_name
  description             = "Lambda function for sending Slack notifications"
  ignore_source_code_hash = true

  environment_variables = {
    SLACK_WEB_HOOK = var.slack_web_hook
  }
  attach_policy = true
  policy        = aws_iam_policy.infra_slacker_lambda_access_to_sqs.arn

  # https://github.com/terraform-aws-modules/terraform-aws-lambda/issues/36
  #   create_current_version_allowed_triggers = false
  image_uri    = "${data.aws_caller_identity.current.account_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com/${var.slacker_image_name}:${var.slacker_image_tag}"
  package_type = "Image"

  create_package = false
  lambda_role    = aws_iam_role.infra_slacker_lambda_access_to_sqs.arn
  role_tags      = merge(var.default_tags, var.tags)

  tags = merge(var.default_tags, var.tags)
}


resource "aws_sqs_queue" "infra_slacker" {
  name                      = var.application_name
  delay_seconds             = 0
  max_message_size          = 2048
  message_retention_seconds = 3600
  receive_wait_time_seconds = 10
  # redrive_policy            = "{\"deadLetterTargetArn\":\"${aws_sqs_queue.terraform_queue_deadletter.arn}\",\"maxReceiveCount\":4}"

  tags = merge(var.default_tags, var.tags)
}

resource "aws_lambda_permission" "sns_can_trigger_start_export_task" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = module.lambda_function_infra_slacker.lambda_function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sqs_queue.infra_slacker.arn
}

resource "aws_lambda_event_source_mapping" "event_source_mapping" {
  event_source_arn = aws_sqs_queue.infra_slacker.arn
  enabled          = true
  function_name    = module.lambda_function_infra_slacker.lambda_function_name
  batch_size       = 1
}


resource "aws_iam_policy" "slacker_client" {
  name        = "${var.application_name}SQSClient"
  path        = "/${var.stage}/${var.application_name}/"
  description = "${var.application_name} Client User"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sqs:SendMessage",
        ]
        Resource = "${aws_sqs_queue.infra_slacker.arn}"
      },
    ]
  })
}

module "slacker_client_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "4.13.1"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

resource "aws_iam_user_policy_attachment" "slacker_client" {
  user       = module.slacker_client_user.iam_user_name
  policy_arn = aws_iam_policy.slacker_client.arn
}
