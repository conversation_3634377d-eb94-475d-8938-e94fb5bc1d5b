module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}

module "vanta" {
  source = "../../../../modules/vanta"
}

module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "northvue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.product}_${var.env}_${data.aws_region.current.name}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

module "northvue_cloudfront_cert" {
  source = "../../../../modules/northvue_cloudfront_cert"
}

resource "aws_iam_user" "northvue_deployer" {
  name = "northvue_deployer"
  tags = var.default_tags
}

resource "aws_iam_user_policy_attachment" "northvue_deployer" {
  user       = aws_iam_user.northvue_deployer.name
  policy_arn = aws_iam_policy.northvue_deployer.arn
}

resource "aws_iam_policy" "northvue_deployer" {
  name = "northvue_deployer-iam-user-policy"

  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
		{
			"Effect": "Allow",
			"Action": [
				"ecr:BatchGet*",
				"ecr:BatchCheck*",
				"ecr:Get*",
				"ecr:Describe*",
				"ecr:List*"
			],
			"Resource": [
			    "*"
			]
		},
    {
      "Effect" : "Allow",
      "Action" : [
        "s3:List*",
        "s3:Get*"
      ],
      "Resource" : [
          "*"
      ]
    }
  ]
}
POLICY
}

module "northvue_monitor_site_configs" {
  source      = "../../../../modules/s3_bucket"
  bucket_name = "northvue-monitor-site-configs-stg"
  region      = data.aws_region.current.name
  tags        = var.default_tags
  acl         = null
}

resource "aws_acm_certificate" "cert_cdn_staging_monitor" {
  domain_name       = "cdn.staging.monitor.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = var.default_tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "cert_api_staging_monitor" {
  domain_name       = "api.staging.monitor.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = var.default_tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_amplify_app" "monitor_dashboard" {
  name         = "monitor-dashboard"
  repository   = "https://bitbucket.org/fingermarkltd/monitor-dashboard"
  access_token = data.vault_generic_secret.amplify_repository_key.data["secret"]
  build_spec   = <<-EOT
    version: 1
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
  EOT

  custom_rule {
    source = "https://staging.monitor.eyecuedashboard.com"
    status = "302"
    target = "https://www.staging.monitor.eyecuedashboard.com"
  }

  custom_rule {
    source = "/<*>"
    status = "404-200"
    target = "/index.html"
  }

  custom_rule {
    source = "</^[^.]+$|\\.(?!(css|gif|ico|jpg|js|png|txt|svg|woff|ttf|map|json)$)([^.]+$)/>"
    status = "200"
    target = "/index.html"
  }

  environment_variables = {
    REACT_APP_AUTH_AUDIENCE     = "https://api.staging.monitor.eyecuedashboard.com",
    REACT_APP_AUTH_CLIENT_ID    = "0EE6d72PMYiev95UD6KkwOrWf2PwBpS1",
    REACT_APP_AUTH_DOMAIN       = "https://dev-fingermark.au.auth0.com",
    REACT_APP_CATCH_API_URL     = "https://api.staging.monitor.eyecuedashboard.com",
    REACT_APP_PUBLIC_VIDEO_HOST = "https://cdn.staging.monitor.eyecuedashboard.com"
  }
}

resource "aws_amplify_domain_association" "amplify_monitor_dashboard_domain" {
  app_id                = aws_amplify_app.monitor_dashboard.id
  domain_name           = "staging.monitor.eyecuedashboard.com"
  wait_for_verification = true

  sub_domain {
    branch_name = "master"
    prefix      = ""
  }

  sub_domain {
    branch_name = "master"
    prefix      = "www"
  }
}

module "eyecue_iot" {
  source         = "../../../../modules/eyecue_iot"
  aws_iam_user   = "eyecue-iot-creator"
  client_name    = var.product
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = data.aws_region.current.name
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}
