module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

module "s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source = "terraform-aws-modules/s3-bucket/aws"

  bucket = "eyecue-${var.client_name}-${var.country}-camera-images"
  #acl                     = "private"
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  versioning = {
    enabled = true
  }
  cors_rule = [{
    allowed_headers = [
      "*"
    ]
    allowed_methods = var.allowed_methods
    allowed_origins = [
      "*"
    ]
    expose_headers  = []
    max_age_seconds = 0
  }]
  tags = merge(var.tags, { "Name" = "eyecue-${var.client_name}-${var.country}-camera-images" })
}


data "aws_iam_policy_document" "eyecue_roi_configurator_camera_images_policy" {
  statement {
    sid = "EyecueRoiConfiguratorS3Policy"
    actions = [
      "s3:DeleteObject",
      "s3:GetObject",
      "s3:ListBucket",
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-camera-images",
    ]
  }
  statement {
    sid = "EyecueRoiConfiguratorDynamoDBPolicy"
    actions = [
      "dynamodb:DeleteItem",
      "dynamodb:GetItem",
      "dynamodb:PutItem",
      "dynamodb:Query",
      "dynamodb:UpdateItem",
      "dynamodb:UpdateTable"
    ]
    resources = [
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-deployer-params",
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-helm-values",
      "arn:aws:dynamodb:${var.aws_region}:${var.aws_account_id}:table/eyecue-things-shadow"
    ]
  }
  statement {
    sid = "EyecueRoiConfiguratorLambdaPolicy"
    actions = [
      "lambda:GetFunction",
      "lambda:InvokeAsync",
      "lambda:InvokeFunction"
    ]
    resources = ["arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:sls-roi-configuration-tool*"]
  }
  statement {
    sid = "EyecueRoiConfiguratorCloudformationPolicy"
    actions = [
      "cloudformation:DescribeStacks"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "eyecue_roi_configurator_camera_images_policy" {
  name   = "EyecueRoiConfiguratorCameraImagesPolicy"
  policy = data.aws_iam_policy_document.eyecue_roi_configurator_camera_images_policy.json
  tags   = var.tags
}

resource "aws_iam_user_policy_attachment" "eyecue_roi_configurator_s3_policy_attachment" {
  user       = var.aws_iam_user
  policy_arn = aws_iam_policy.eyecue_roi_configurator_camera_images_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
