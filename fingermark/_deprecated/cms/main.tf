module "stage_subdomain" {
  source                  = "../../../modules/cloudflare"
  cloudflare_record_name  = "stage.cms"
  cloudflare_record_value = "app-cms-stage-eastus.azurewebsites.net"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

module "dev_subdomain" {
  source                  = "../../../modules/cloudflare"
  cloudflare_record_name  = "dev.cms"
  cloudflare_record_value = "app-cms-dev-eastus.azurewebsites.net"
  cloudflare_api_key      = var.cloudflare_api_key
  cloudflare_record_type  = "CNAME"
}