module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}

module "s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source = "terraform-aws-modules/s3-bucket/aws"

  bucket = "eyecue-${var.client_name}-${var.country}-logs"
  # acl                     = "private"
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  versioning = {
    enabled = var.enable_versioning
  }
  lifecycle_rule = [
    {
      id      = "Delete older than 90 days"
      enabled = true
      filter = {
        prefix = ""
      }
      abort_incomplete_multipart_upload_days = 90
      expiration = {
        days = 90
      }
      noncurrent_version_expiration = {
        days = 90
      }
    }
  ]
  tags = merge(var.tags, { "Name" = "eyecue-${var.client_name}-${var.country}-logs" })
}

data "aws_iam_policy_document" "eyecue_logs_sync_policy" {
  statement {
    sid = "EyecueLogSyncIAMPolicy"
    actions = [
      "s3:PutObject",
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-logs",
      "arn:aws:s3:::eyecue-${var.client_name}-${var.country}-logs/*"
    ]
  }
}

resource "aws_iam_policy" "eyecue_logs_synchroniser_policy" {
  name        = "EyecueLogsSynchroniserPolicy"
  depends_on  = [module.iam_user]
  path        = "/"
  description = ""

  policy = data.aws_iam_policy_document.eyecue_logs_sync_policy.json
}

resource "aws_iam_user_policy_attachment" "eyecue_logs_sync_attachment" {
  user       = var.aws_iam_user
  policy_arn = aws_iam_policy.eyecue_logs_synchroniser_policy.arn
}

resource "aws_secretsmanager_secret" "credentials" {
  name = "${var.aws_iam_user}-credentials"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_secretsmanager_secret_version" "credentials" {
  secret_id = aws_secretsmanager_secret.credentials.id
  secret_string = jsonencode({
    "iam_user_name"         = module.iam_user.this_iam_user_name,
    "aws_access_key_id"     = module.iam_user.this_iam_access_key_id,
    "aws_secret_access_key" = module.iam_user.this_iam_access_key_encrypted_secret,
    "keybase_command"       = module.iam_user.keybase_secret_key_decrypt_command
  })
}
