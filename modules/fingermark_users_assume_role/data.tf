data "aws_caller_identity" "current" {}

data "aws_iam_policy_document" "instance_assume_role_deployer_policy" {
  statement {
    sid = "1"
    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }
  statement {
    sid = "2"
    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    condition {
      test     = "StringLike"
      variable = "api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc:sub"
      values   = ["*"]
    }
    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/api.bitbucket.org/2.0/workspaces/fingermarkltd/pipelines-config/identity/oidc"]
    }

  }
}

data "aws_iam_policy_document" "instance_assume_role_policy" {
  statement {
    sid = "1"

    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }
}

data "aws_iam_policy_document" "instance_assume_role_policy_admin_access" {
  statement {
    sid = "1"

    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.trusted_aws_account_id}:root"]
    }
  }

  statement {
    sid = "2"

    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/kubeiam-atlantis"]
    }
  }
}