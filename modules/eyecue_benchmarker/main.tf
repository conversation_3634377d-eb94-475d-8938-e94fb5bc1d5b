module "iam_user" {
  # https://registry.terraform.io/modules/terraform-aws-modules/iam/aws/latest/submodules/iam-user?tab=inputs
  source                        = "terraform-aws-modules/iam/aws//modules/iam-user"
  version                       = "~> 3.0"
  name                          = var.aws_iam_user
  create_iam_access_key         = true
  create_iam_user_login_profile = false
  force_destroy                 = true
  password_reset_required       = true
  pgp_key                       = var.keybase
  tags                          = var.tags
}


module "s3_bucket" {
  # https://registry.terraform.io/modules/terraform-aws-modules/s3-bucket/aws/latest
  source = "terraform-aws-modules/s3-bucket/aws"

  bucket                  = var.s3_bucket_name
  acl                     = "private"
  block_public_policy     = true
  block_public_acls       = true
  restrict_public_buckets = true
  ignore_public_acls      = true
  versioning = {
    enabled = true
  }
  cors_rule = [{
    allowed_headers = [
      "*"
    ]
    allowed_methods = var.allowed_methods
    allowed_origins = [
      "*"
    ]
    expose_headers  = []
    max_age_seconds = 0
  }]

  tags = var.tags
}


data "aws_iam_policy_document" "eyecue_benchmarker" {

  statement {
    sid = "BenchmarkerS3Policy"
    actions = [
      "s3:*"
    ]
    resources = [
      "arn:aws:s3:::${var.s3_bucket_name}/*",
      "arn:aws:s3:::${var.s3_bucket_name}"
    ]
  }
  statement {
    sid = "BenchmarkerLogsPolicy"
    actions = [
      "logs:*"
    ]
    resources = [
      "arn:aws:logs:*:*:*"
    ]
  }
  statement {
    sid = "BenchmarkerLambdaPolicy"
    actions = [
      "lambda:InvokeFunction"
    ]
    resources = [
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-things-shadow-get-config-server",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyeq-benchmarker-*",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:get_benckmarking_all_pending_items",
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:eyecue-weights-get_weights"
    ]
  }
  statement {
    sid = "BenchmarkerSQSPolicy"
    actions = [
      "sqs:*"
    ]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${var.aws_account_id}:benchmarker"
    ]
  }
  statement {
    sid = "BenchmarkerStatesPolicy"
    actions = [
      "states:SendTaskHeartbeat",
      "states:SendTaskSuccess",
      "states:SendTaskFailure"
    ]
    resources = [
      "arn:aws:states:${var.aws_region}:${var.aws_account_id}:stateMachine:benchmarker"
    ]
  }
}

resource "aws_iam_policy" "eyecue_benchmarker_policy" {
  name       = "EyecueBenchmarkerPolicy"
  depends_on = [module.iam_user]
  policy     = data.aws_iam_policy_document.eyecue_benchmarker.json
  tags       = var.tags
}

resource "aws_iam_user_policy_attachment" "eyecue_benchmarker_attachment" {
  user       = var.aws_iam_user
  policy_arn = aws_iam_policy.eyecue_benchmarker_policy.arn
}
